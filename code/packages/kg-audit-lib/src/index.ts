/**
 * @fileoverview kg-audit-lib - Knowledge Graph Audit Library
 * @implements milestone-M2#AuditLibrary
 * 
 * Provides audit functionality for knowledge graphs including:
 * - Coverage calculation (extends kg-sync-lib)
 * - Unknown edge detection
 * - Audit report generation
 * - JSON schema validation
 */

// Re-export core types from kg-sync-lib for convenience
export type {
  MilestoneCoverage,
  KnowledgeGraphNode,
  KnowledgeGraphEdge,
} from '@workflow-mapper/kg-sync-lib';

// Export audit-specific functionality (to be implemented)
export { calculateAuditCoverage } from './coverage.js';
export { detectUnknownEdges } from './unknownEdges.js';
export { generateAuditReport, validateAuditReport } from './auditReport.js';

// Export types
export type {
  AuditReport,
  AuditOptions,
  UnknownEdge,
  AuditCoverage,
} from './types.js';
