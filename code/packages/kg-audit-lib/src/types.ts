/**
 * @fileoverview Type definitions for kg-audit-lib
 * @implements milestone-M2#AuditTypes
 */

import type { MilestoneCoverage } from '@workflow-mapper/kg-sync-lib';

/**
 * Options for audit operations
 */
export interface AuditOptions {
  since?: string;
  threshold?: number;
  includePerformance?: boolean;
  format?: 'json' | 'pretty' | 'both';
}

/**
 * Unknown edge detected in the knowledge graph
 */
export interface UnknownEdge {
  type: 'workflow_calls' | 'implements';
  source: string;
  target: string;
  confidence: number;
  reason: 'missing_target' | 'stale_spec' | 'parse_error';
  filePath?: string;
  lineNumber?: number;
}

/**
 * Extended milestone coverage with audit-specific metrics
 */
export interface AuditCoverage extends MilestoneCoverage {
  unknownEdgeCount: number;
  auditTimestamp: string;
  components: {
    total: number;
    implemented: number;
    stale: number;
  };
}

/**
 * Complete audit report structure
 */
export interface AuditReport {
  summary: {
    generatedAt: string;
    edgeTotals: {
      implements: number;
      workflow_calls: number;
      dependsOn: number;
      total: number;
    };
    gitRef: string;
    filesScanned: number;
  };
  milestones: AuditCoverage[];
  unknownEdges: UnknownEdge[];
  performance: {
    durationMs: number;
    filesProcessed: number;
    edgesAnalyzed: number;
    cacheHits?: number;
  };
}

/**
 * Audit report validation result
 */
export interface AuditValidationResult {
  isValid: boolean;
  errors: string[];
}
