/**
 * @fileoverview Unknown edge detection for knowledge graph audit
 * @implements milestone-M2#UnknownEdgeDetection
 */

import type { KnowledgeGraphNode, KnowledgeGraphEdge } from '@workflow-mapper/kg-sync-lib';
import type { UnknownEdge } from './types.js';

// Extended edge interface for audit functionality
interface AuditEdge extends Omit<KnowledgeGraphEdge, '@type'> {
  '@type': 'implements' | 'dependsOn' | 'contains' | 'workflow_calls';
  filePath?: string;
  lineNumber?: number;
}

/**
 * Detect unknown edges in the knowledge graph
 * @implements milestone-M2#UnknownEdgeDetection
 * @param nodes - Knowledge graph nodes
 * @param edges - Knowledge graph edges
 * @returns Array of unknown edges detected
 */
export function detectUnknownEdges(
  nodes: KnowledgeGraphNode[],
  edges: AuditEdge[]
): UnknownEdge[] {
  const unknownEdges: UnknownEdge[] = [];

  // 1. Check workflow_calls for missing targets
  const workflowCallEdges = edges.filter(e => e['@type'] === 'workflow_calls');
  for (const edge of workflowCallEdges) {
    const targetExists = checkWorkflowTargetExists(edge.target, nodes);
    if (!targetExists) {
      unknownEdges.push({
        type: 'workflow_calls',
        source: edge.source,
        target: edge.target,
        confidence: 0.2,
        reason: 'missing_target',
        filePath: edge.filePath,
        lineNumber: edge.lineNumber
      });
    }
  }

  // 2. Check implements edges for stale specs
  const implementsEdges = edges.filter(e => e['@type'] === 'implements');
  for (const edge of implementsEdges) {
    const specExists = checkSpecExists(edge.target, nodes);
    const componentExists = checkComponentExists(edge.target, nodes);

    if (!specExists) {
      unknownEdges.push({
        type: 'implements',
        source: edge.source,
        target: edge.target,
        confidence: 0.2,
        reason: 'stale_spec',
        filePath: edge.filePath,
        lineNumber: edge.lineNumber
      });
    } else if (!componentExists) {
      unknownEdges.push({
        type: 'implements',
        source: edge.source,
        target: edge.target,
        confidence: 0.2,
        reason: 'missing_target',
        filePath: edge.filePath,
        lineNumber: edge.lineNumber
      });
    }
  }

  return unknownEdges;
}

/**
 * Check if workflow call target exists in nodes
 */
function checkWorkflowTargetExists(target: string, nodes: KnowledgeGraphNode[]): boolean {
  return nodes.some(node => node['@id'] === target || (node.functionName && node.functionName === target));
}

/**
 * Check if milestone spec exists
 */
function checkSpecExists(target: string, nodes: KnowledgeGraphNode[]): boolean {
  const milestoneMatch = target.match(/milestone-([^#]+)/);
  if (!milestoneMatch || !milestoneMatch[1]) return false;

  const milestoneId = milestoneMatch[1];
  return nodes.some(node => {
    const nodeId = node['@id'];
    return node['@type'] === 'Milestone' && nodeId && String(nodeId).includes(milestoneId);
  });
}

/**
 * Check if component exists in milestone spec
 */
function checkComponentExists(target: string, nodes: KnowledgeGraphNode[]): boolean {
  const match = target.match(/milestone-([^#]+)#(.+)/);
  if (!match || !match[1] || !match[2]) return false;

  const [, milestoneId, componentName] = match;

  const milestoneNode = nodes.find(node => {
    const nodeId = node['@id'];
    return node['@type'] === 'Milestone' && nodeId && String(nodeId).includes(milestoneId);
  });

  if (!milestoneNode) return false;

  // Check if component is mentioned in milestone content
  const content = milestoneNode.content;
  return content ? String(content).includes(componentName) : false;
}
