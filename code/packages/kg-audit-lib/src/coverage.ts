/**
 * @fileoverview Coverage calculation extensions for audit functionality
 * @implements milestone-M2#AuditCoverage
 */

import {
  calculateCoverage,
  type MilestoneCoverage,
  type KnowledgeGraphEdge,
} from '@workflow-mapper/kg-sync-lib';
import type { AuditCoverage } from './types.js';

/**
 * Calculate audit-specific coverage metrics extending kg-sync-lib functionality
 * @implements milestone-M2#CoverageCalculation
 * @param milestoneId - Milestone identifier
 * @param implementsEdges - Implements edges for the milestone
 * @param totalComponents - Total number of components in milestone
 * @param unknownEdgeCount - Number of unknown edges detected
 * @returns Extended coverage metrics with audit information
 */
export function calculateAuditCoverage(
  milestoneId: string,
  implementsEdges: KnowledgeGraphEdge[],
  totalComponents: number,
  unknownEdgeCount: number = 0
): AuditCoverage {
  // Reuse existing coverage calculation (95% reuse)
  const baseCoverage = calculateCoverage(milestoneId, implementsEdges, totalComponents);

  // Extend with audit-specific metrics
  const auditCoverage: AuditCoverage = {
    ...baseCoverage,
    unknownEdgeCount,
    auditTimestamp: new Date().toISOString(),
    components: {
      total: totalComponents,
      implemented: baseCoverage.implementedComponents,
      stale: implementsEdges.filter(edge => edge.confidence <= 0.2).length,
    },
  };

  return auditCoverage;
}
